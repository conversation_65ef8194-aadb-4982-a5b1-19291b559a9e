/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZfbm90LWZvdW5kJTJGcGFnZSZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlJmFwcFBhdGhzPSZwYWdlUGF0aD1ub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtZXJyb3IuanMmYXBwRGlyPUUlM0ElNUNUZW1wbyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1Q0RlYWxWZXJzZSUyME9TJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUUlM0ElNUNUZW1wbyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1Q0RlYWxWZXJzZSUyME9TJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBLHNCQUFzQiwwTkFBZ0Y7QUFDdEc7QUFDQTtBQUNBLGFBQWE7QUFDYixXQUFXLElBQUk7QUFDZixTQUFTO0FBQ1Q7QUFDQSx5QkFBeUIsNElBQTRKO0FBQ3JMLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvP2M0ODIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICAgIGNoaWxkcmVuOiBbXCIvX25vdC1mb3VuZFwiLCB7XG4gICAgICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgICAgIHBhZ2U6IFtcbiAgICAgICAgICAgICAgICAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksXG4gICAgICAgICAgICAgICAgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJcbiAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgfV1cbiAgICAgICAgICB9LCB7fV1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcVGVtcG9cXFxcTWljcm8gU2FhcyBSZXNlYXJjaFxcXFxSZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllc1xcXFxEZWFsVmVyc2UgT1NcXFxcRGVhbFZlcnNlIE9TIEFwcFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJFOlxcXFxUZW1wb1xcXFxNaWNybyBTYWFzIFJlc2VhcmNoXFxcXFJlc2VhcmNoZXMgb24gTWljcm8gU0FBUyBPcHBvcnR1bml0aWVzXFxcXERlYWxWZXJzZSBPU1xcXFxEZWFsVmVyc2UgT1MgQXBwXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL19ub3QtZm91bmQvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9fbm90LWZvdW5kL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL19ub3QtZm91bmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, defaultTheme = \"light\", enableSystem = true, disableTransitionOnChange = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: defaultTheme,\n        enableSystem: enableSystem,\n        disableTransitionOnChange: disableTransitionOnChange,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRTFELFNBQVNDLGNBQWMsRUFDNUJFLFFBQVEsRUFDUkMsZUFBZSxPQUFPLEVBQ3RCQyxlQUFlLElBQUksRUFDbkJDLDRCQUE0QixLQUFLLEVBTWxDO0lBQ0MscUJBQ0UsOERBQUNKLHNEQUFrQkE7UUFDakJLLFdBQVU7UUFDVkgsY0FBY0E7UUFDZEMsY0FBY0E7UUFDZEMsMkJBQTJCQTtrQkFFMUJIOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWx2ZXJzZS1vcy8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgZGVmYXVsdFRoZW1lID0gXCJsaWdodFwiLFxuICBlbmFibGVTeXN0ZW0gPSB0cnVlLFxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlID0gZmFsc2Vcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBkZWZhdWx0VGhlbWU/OiBzdHJpbmdcbiAgZW5hYmxlU3lzdGVtPzogYm9vbGVhblxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPzogYm9vbGVhblxufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0VGhlbWVzUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT17ZGVmYXVsdFRoZW1lfVxuICAgICAgZW5hYmxlU3lzdGVtPXtlbmFibGVTeXN0ZW19XG4gICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPXtkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRUaGVtZXNQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsImF0dHJpYnV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/**\n * API Client for DealVerse OS Backend\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.token = null;\n        this.baseUrl = baseUrl;\n        // Get token from localStorage if available\n        if (false) {}\n    }\n    setToken(token) {\n        this.token = token;\n        if (false) {}\n    }\n    clearToken() {\n        this.token = null;\n        if (false) {}\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...options.headers || {}\n        };\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token expired, try to refresh\n                    const refreshed = await this.refreshToken();\n                    if (refreshed) {\n                        // Retry the request with new token\n                        headers.Authorization = `Bearer ${this.token}`;\n                        const retryResponse = await fetch(url, {\n                            ...options,\n                            headers\n                        });\n                        if (retryResponse.ok) {\n                            const data = await retryResponse.json();\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // Refresh failed, clear tokens and redirect to login\n                    this.clearToken();\n                    if (false) {}\n                    throw new Error(\"Authentication failed\");\n                }\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            // Check if it's a network error\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                return {\n                    error: \"Unable to connect to server. Please check your connection and try again.\"\n                };\n            }\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async refreshToken() {\n        const refreshToken =  false ? 0 : null;\n        if (!refreshToken) return false;\n        try {\n            const response = await fetch(`${this.baseUrl}/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                this.setToken(data.access_token);\n                if (false) {}\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Token refresh failed:\", error);\n        }\n        return false;\n    }\n    // Authentication endpoints\n    async login(email, password) {\n        return this.request(\"/auth/login/json\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    }\n    async register(userData) {\n        return this.request(\"/auth/register\", {\n            method: \"POST\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async logout() {\n        const result = await this.request(\"/auth/logout\", {\n            method: \"POST\"\n        });\n        this.clearToken();\n        return result;\n    }\n    // User endpoints\n    async getCurrentUser() {\n        return this.request(\"/users/me\");\n    }\n    async updateCurrentUser(userData) {\n        return this.request(\"/users/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async getUsers(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/users?${query.toString()}`);\n    }\n    // Deal endpoints\n    async getDeals(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.stage) query.append(\"stage\", params.stage);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/deals?${query.toString()}`);\n    }\n    async getDeal(id) {\n        return this.request(`/deals/${id}`);\n    }\n    async createDeal(dealData) {\n        return this.request(\"/deals\", {\n            method: \"POST\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async updateDeal(id, dealData) {\n        return this.request(`/deals/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async deleteDeal(id) {\n        return this.request(`/deals/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async getDealsStats() {\n        return this.request(\"/deals/stats/summary\");\n    }\n    // Client endpoints\n    async getClients(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.client_type) query.append(\"client_type\", params.client_type);\n        if (params?.industry) query.append(\"industry\", params.industry);\n        if (params?.search) query.append(\"search\", params.search);\n        return this.request(`/clients?${query.toString()}`);\n    }\n    async getClient(id) {\n        return this.request(`/clients/${id}`);\n    }\n    async createClient(clientData) {\n        return this.request(\"/clients\", {\n            method: \"POST\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async updateClient(id, clientData) {\n        return this.request(`/clients/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async deleteClient(id) {\n        return this.request(`/clients/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Analytics endpoints\n    async getDashboardAnalytics() {\n        return this.request(\"/analytics/dashboard\");\n    }\n    async getDealsPerformance(days = 30) {\n        return this.request(`/analytics/deals/performance?days=${days}`);\n    }\n    async getClientInsights() {\n        return this.request(\"/analytics/clients/insights\");\n    }\n    async getTeamProductivity() {\n        return this.request(\"/analytics/team/productivity\");\n    }\n    // Document endpoints\n    async getDocuments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.document_type) query.append(\"document_type\", params.document_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/documents?${query.toString()}`);\n    }\n    async getDocument(id) {\n        return this.request(`/documents/${id}`);\n    }\n    async createDocument(documentData) {\n        return this.request(\"/documents\", {\n            method: \"POST\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async uploadDocument(file, metadata) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        formData.append(\"title\", metadata.title);\n        if (metadata.document_type) formData.append(\"document_type\", metadata.document_type);\n        if (metadata.deal_id) formData.append(\"deal_id\", metadata.deal_id);\n        if (metadata.client_id) formData.append(\"client_id\", metadata.client_id);\n        if (metadata.is_confidential !== undefined) formData.append(\"is_confidential\", metadata.is_confidential.toString());\n        const headers = {};\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(`${this.baseUrl}/documents/upload`, {\n                method: \"POST\",\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"Document upload failed:\", error);\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async updateDocument(id, documentData) {\n        return this.request(`/documents/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async deleteDocument(id) {\n        return this.request(`/documents/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Document Analysis endpoints\n    async analyzeDocument(id, analysisType, focusAreas) {\n        return this.request(`/documents/${id}/analyze`, {\n            method: \"POST\",\n            body: JSON.stringify({\n                analysis_type: analysisType || \"full\",\n                focus_areas: focusAreas || [\n                    \"financial\",\n                    \"legal\",\n                    \"risk\"\n                ]\n            })\n        });\n    }\n    async getDocumentAnalysis(id) {\n        return this.request(`/documents/${id}/analysis`);\n    }\n    async assessDocumentRisk(documentIds, assessmentType) {\n        return this.request(\"/documents/risk-assessment\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                document_ids: documentIds,\n                assessment_type: assessmentType || \"comprehensive\"\n            })\n        });\n    }\n    async getRiskAssessments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/documents/risk-assessments?${query}`);\n    }\n    async categorizeDocument(id) {\n        return this.request(`/documents/${id}/categorize`);\n    }\n    async getDocumentAnalytics() {\n        return this.request(\"/documents/analytics/statistics\");\n    }\n    async getHighRiskDocuments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.risk_threshold) query.append(\"risk_threshold\", params.risk_threshold.toString());\n        return this.request(`/documents/high-risk?${query}`);\n    }\n    // Financial Models endpoints\n    async getFinancialModels(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.model_type) query.append(\"model_type\", params.model_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/financial-models?${query.toString()}`);\n    }\n    async getFinancialModel(id) {\n        return this.request(`/financial-models/${id}`);\n    }\n    async createFinancialModel(modelData) {\n        return this.request(\"/financial-models\", {\n            method: \"POST\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async updateFinancialModel(id, modelData) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async deleteFinancialModel(id) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createModelVersion(id, versionData) {\n        return this.request(`/financial-models/${id}/versions`, {\n            method: \"POST\",\n            body: JSON.stringify(versionData)\n        });\n    }\n    async getModelVersions(id) {\n        return this.request(`/financial-models/${id}/versions`);\n    }\n    async getModelStatistics() {\n        return this.request(\"/financial-models/statistics\");\n    }\n    // Organization endpoints\n    async getCurrentOrganization() {\n        return this.request(\"/organizations/me\");\n    }\n    async updateCurrentOrganization(orgData) {\n        return this.request(\"/organizations/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(orgData)\n        });\n    }\n    async getOrganizationStats() {\n        return this.request(\"/organizations/me/stats\");\n    }\n    // Presentation endpoints\n    async getPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.status) query.append(\"status\", params.status);\n        if (params?.presentation_type) query.append(\"presentation_type\", params.presentation_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.created_by_me) query.append(\"created_by_me\", params.created_by_me.toString());\n        return this.request(`/presentations/?${query.toString()}`);\n    }\n    async getPresentation(id) {\n        return this.request(`/presentations/${id}`);\n    }\n    async createPresentation(presentationData) {\n        return this.request(\"/presentations/\", {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async updatePresentation(id, presentationData) {\n        return this.request(`/presentations/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async deletePresentation(id) {\n        return this.request(`/presentations/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createPresentationVersion(id) {\n        return this.request(`/presentations/${id}/version`, {\n            method: \"POST\"\n        });\n    }\n    async getPresentationsByDeal(dealId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/deals/${dealId}?${query.toString()}`);\n    }\n    async getSharedPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/shared?${query.toString()}`);\n    }\n    // Slide endpoints\n    async getPresentationSlides(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/slides?${query.toString()}`);\n    }\n    async createSlide(presentationId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/`, {\n            method: \"POST\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async getSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`);\n    }\n    async updateSlide(presentationId, slideId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"PUT\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async deleteSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async duplicateSlide(presentationId, slideId, newSlideNumber) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}/duplicate?new_slide_number=${newSlideNumber}`, {\n            method: \"POST\"\n        });\n    }\n    // Template endpoints\n    async getPresentationTemplates(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.category) query.append(\"category\", params.category);\n        if (params?.featured_only) query.append(\"featured_only\", params.featured_only.toString());\n        if (params?.public_only) query.append(\"public_only\", params.public_only.toString());\n        return this.request(`/presentations/templates/?${query.toString()}`);\n    }\n    async createPresentationTemplate(templateData) {\n        return this.request(\"/presentations/templates/\", {\n            method: \"POST\",\n            body: JSON.stringify(templateData)\n        });\n    }\n    async getPresentationTemplate(id) {\n        return this.request(`/presentations/templates/${id}`);\n    }\n    async createPresentationFromTemplate(templateId, presentationData) {\n        return this.request(`/presentations/templates/${templateId}/use`, {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    // Comment endpoints\n    async getPresentationComments(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.resolved_only !== undefined) query.append(\"resolved_only\", params.resolved_only.toString());\n        return this.request(`/presentations/${presentationId}/comments?${query.toString()}`);\n    }\n    async createPresentationComment(presentationId, commentData) {\n        return this.request(`/presentations/${presentationId}/comments/`, {\n            method: \"POST\",\n            body: JSON.stringify(commentData)\n        });\n    }\n    async resolvePresentationComment(presentationId, commentId) {\n        return this.request(`/presentations/${presentationId}/comments/${commentId}/resolve`, {\n            method: \"PUT\"\n        });\n    }\n    // Collaboration endpoints\n    async getPresentationActivities(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/activities?${query.toString()}`);\n    }\n    async getPresentationActiveUsers(presentationId) {\n        return this.request(`/presentations/${presentationId}/active-users`);\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is already logged in\n        const token = localStorage.getItem(\"access_token\");\n        if (token) {\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(token);\n            refreshUser();\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const refreshUser = async ()=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.getCurrentUser();\n            if (response.data) {\n                setUser(response.data);\n            } else {\n                // Token is invalid, clear it\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.login(email, password);\n            if (response.data) {\n                const { access_token, refresh_token } = response.data;\n                // Store tokens\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(access_token);\n                localStorage.setItem(\"refresh_token\", refresh_token);\n                // Get user data\n                await refreshUser();\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Login failed\"\n            };\n        }\n    };\n    const logout = ()=>{\n        _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.logout();\n        setUser(null);\n        // Redirect to login page\n        window.location.href = \"/auth/login\";\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protecting routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login\n            if (false) {}\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37beebff4f30\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvLi9hcHAvZ2xvYmFscy5jc3M/NmZkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3YmVlYmZmNGYzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"DealVerse OS - Investment Banking Platform\",\n    description: \"AI-powered investment banking platform with deal sourcing, due diligence, valuation modeling, compliance management, and presentation tools.\",\n    keywords: \"investment banking, deal sourcing, due diligence, valuation, compliance, AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#withAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-themes","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();