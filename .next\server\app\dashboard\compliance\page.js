/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/compliance/page";
exports.ids = ["app/dashboard/compliance/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'compliance',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/compliance/page.tsx */ \"(rsc)/./app/dashboard/compliance/page.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/compliance/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/compliance/page\",\n        pathname: \"/dashboard/compliance\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/compliance/page.tsx */ \"(ssr)/./app/dashboard/compliance/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wbyU1QyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1QyU1Q0RlYWxWZXJzZSUyME9TJTVDJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNjb21wbGlhbmNlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUFpTCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWx2ZXJzZS1vcy8/OGY2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXFRlbXBvXFxcXE1pY3JvIFNhYXMgUmVzZWFyY2hcXFxcUmVzZWFyY2hlcyBvbiBNaWNybyBTQUFTIE9wcG9ydHVuaXRpZXNcXFxcRGVhbFZlcnNlIE9TXFxcXERlYWxWZXJzZSBPUyBBcHBcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxjb21wbGlhbmNlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(ssr)/./app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wbyU1QyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1QyU1Q0RlYWxWZXJzZSUyME9TJTVDJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBdUsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvP2UwY2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxUZW1wb1xcXFxNaWNybyBTYWFzIFJlc2VhcmNoXFxcXFJlc2VhcmNoZXMgb24gTWljcm8gU0FBUyBPcHBvcnR1bml0aWVzXFxcXERlYWxWZXJzZSBPU1xcXFxEZWFsVmVyc2UgT1MgQXBwXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wbyU1QyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1QyU1Q0RlYWxWZXJzZSUyME9TJTVDJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wbyU1QyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1QyU1Q0RlYWxWZXJzZSUyME9TJTVDJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDVGVtcG8lNUMlNUNNaWNybyUyMFNhYXMlMjBSZXNlYXJjaCU1QyU1Q1Jlc2VhcmNoZXMlMjBvbiUyME1pY3JvJTIwU0FBUyUyME9wcG9ydHVuaXRpZXMlNUMlNUNEZWFsVmVyc2UlMjBPUyU1QyU1Q0RlYWxWZXJzZSUyME9TJTIwQXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1RlbXBvJTVDJTVDTWljcm8lMjBTYWFzJTIwUmVzZWFyY2glNUMlNUNSZXNlYXJjaGVzJTIwb24lMjBNaWNybyUyMFNBQVMlMjBPcHBvcnR1bml0aWVzJTVDJTVDRGVhbFZlcnNlJTIwT1MlNUMlNUNEZWFsVmVyc2UlMjBPUyUyMEFwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1RlbXBvJTVDJTVDTWljcm8lMjBTYWFzJTIwUmVzZWFyY2glNUMlNUNSZXNlYXJjaGVzJTIwb24lMjBNaWNybyUyMFNBQVMlMjBPcHBvcnR1bml0aWVzJTVDJTVDRGVhbFZlcnNlJTIwT1MlNUMlNUNEZWFsVmVyc2UlMjBPUyUyMEFwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDVGVtcG8lNUMlNUNNaWNybyUyMFNhYXMlMjBSZXNlYXJjaCU1QyU1Q1Jlc2VhcmNoZXMlMjBvbiUyME1pY3JvJTIwU0FBUyUyME9wcG9ydHVuaXRpZXMlNUMlNUNEZWFsVmVyc2UlMjBPUyU1QyU1Q0RlYWxWZXJzZSUyME9TJTIwQXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXdNO0FBQ3hNO0FBQ0Esb09BQXlNO0FBQ3pNO0FBQ0EsME9BQTRNO0FBQzVNO0FBQ0Esd09BQTJNO0FBQzNNO0FBQ0Esa1BBQWdOO0FBQ2hOO0FBQ0Esc1FBQTBOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLz9jNmY1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcVGVtcG9cXFxcTWljcm8gU2FhcyBSZXNlYXJjaFxcXFxSZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllc1xcXFxEZWFsVmVyc2UgT1NcXFxcRGVhbFZlcnNlIE9TIEFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXFRlbXBvXFxcXE1pY3JvIFNhYXMgUmVzZWFyY2hcXFxcUmVzZWFyY2hlcyBvbiBNaWNybyBTQUFTIE9wcG9ydHVuaXRpZXNcXFxcRGVhbFZlcnNlIE9TXFxcXERlYWxWZXJzZSBPUyBBcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcVGVtcG9cXFxcTWljcm8gU2FhcyBSZXNlYXJjaFxcXFxSZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllc1xcXFxEZWFsVmVyc2UgT1NcXFxcRGVhbFZlcnNlIE9TIEFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxUZW1wb1xcXFxNaWNybyBTYWFzIFJlc2VhcmNoXFxcXFJlc2VhcmNoZXMgb24gTWljcm8gU0FBUyBPcHBvcnR1bml0aWVzXFxcXERlYWxWZXJzZSBPU1xcXFxEZWFsVmVyc2UgT1MgQXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcVGVtcG9cXFxcTWljcm8gU2FhcyBSZXNlYXJjaFxcXFxSZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllc1xcXFxEZWFsVmVyc2UgT1NcXFxcRGVhbFZlcnNlIE9TIEFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcVGVtcG9cXFxcTWljcm8gU2FhcyBSZXNlYXJjaFxcXFxSZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllc1xcXFxEZWFsVmVyc2UgT1NcXFxcRGVhbFZlcnNlIE9TIEFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/compliance/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/compliance/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CompliancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,Clock,Download,FileText,RefreshCw,Settings,Shield,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Mock data for compliance dashboard\nconst complianceMetrics = [\n    {\n        category: \"SEC Compliance\",\n        status: \"compliant\",\n        score: 98,\n        lastReview: \"2 days ago\",\n        nextReview: \"30 days\",\n        requirements: 12,\n        completed: 12\n    },\n    {\n        category: \"FINRA Regulations\",\n        status: \"warning\",\n        score: 85,\n        lastReview: \"1 week ago\",\n        nextReview: \"15 days\",\n        requirements: 8,\n        completed: 7\n    },\n    {\n        category: \"Anti-Money Laundering\",\n        status: \"compliant\",\n        score: 95,\n        lastReview: \"3 days ago\",\n        nextReview: \"45 days\",\n        requirements: 15,\n        completed: 15\n    },\n    {\n        category: \"Data Privacy (GDPR)\",\n        status: \"action_required\",\n        score: 72,\n        lastReview: \"2 weeks ago\",\n        nextReview: \"7 days\",\n        requirements: 10,\n        completed: 8\n    }\n];\nconst auditTrail = [\n    {\n        id: \"1\",\n        action: \"Document Review Completed\",\n        user: \"Sarah Chen\",\n        timestamp: \"2024-01-15 14:30:00\",\n        category: \"Due Diligence\",\n        details: \"Completed review of financial statements for TechFlow Industries\",\n        riskLevel: \"low\"\n    },\n    {\n        id: \"2\",\n        action: \"Compliance Check Failed\",\n        user: \"System\",\n        timestamp: \"2024-01-15 12:15:00\",\n        category: \"FINRA\",\n        details: \"Missing required disclosure documentation\",\n        riskLevel: \"high\"\n    },\n    {\n        id: \"3\",\n        action: \"Policy Update Applied\",\n        user: \"Michael Rodriguez\",\n        timestamp: \"2024-01-15 10:45:00\",\n        category: \"AML\",\n        details: \"Updated customer verification procedures\",\n        riskLevel: \"medium\"\n    },\n    {\n        id: \"4\",\n        action: \"Training Completed\",\n        user: \"Emily Watson\",\n        timestamp: \"2024-01-14 16:20:00\",\n        category: \"Compliance Training\",\n        details: \"Completed annual compliance training module\",\n        riskLevel: \"low\"\n    }\n];\nconst regulatoryUpdates = [\n    {\n        title: \"SEC Updates Investment Adviser Rules\",\n        date: \"2024-01-10\",\n        category: \"SEC\",\n        impact: \"High\",\n        summary: \"New requirements for investment adviser marketing rules effective March 2024\",\n        status: \"pending_review\"\n    },\n    {\n        title: \"FINRA Enhances Cybersecurity Guidelines\",\n        date: \"2024-01-08\",\n        category: \"FINRA\",\n        impact: \"Medium\",\n        summary: \"Updated cybersecurity requirements for broker-dealers\",\n        status: \"implemented\"\n    },\n    {\n        title: \"AML Reporting Threshold Changes\",\n        date: \"2024-01-05\",\n        category: \"FinCEN\",\n        impact: \"High\",\n        summary: \"Revised suspicious activity reporting thresholds\",\n        status: \"under_review\"\n    }\n];\nconst complianceChecklist = [\n    {\n        item: \"Annual Form ADV Filing\",\n        dueDate: \"2024-03-31\",\n        status: \"pending\",\n        priority: \"high\"\n    },\n    {\n        item: \"Quarterly AML Review\",\n        dueDate: \"2024-01-31\",\n        status: \"in_progress\",\n        priority: \"medium\"\n    },\n    {\n        item: \"Employee Training Certification\",\n        dueDate: \"2024-02-15\",\n        status: \"completed\",\n        priority: \"low\"\n    },\n    {\n        item: \"Data Backup Verification\",\n        dueDate: \"2024-01-20\",\n        status: \"pending\",\n        priority: \"high\"\n    },\n    {\n        item: \"Client Privacy Notice Update\",\n        dueDate: \"2024-02-28\",\n        status: \"pending\",\n        priority: \"medium\"\n    }\n];\nfunction CompliancePage() {\n    const [selectedMetric, setSelectedMetric] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(complianceMetrics[0]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"compliant\":\n                return \"text-dealverse-green\";\n            case \"warning\":\n                return \"text-dealverse-amber\";\n            case \"action_required\":\n                return \"text-dealverse-coral\";\n            default:\n                return \"text-dealverse-medium-gray\";\n        }\n    };\n    const getStatusBg = (status)=>{\n        switch(status){\n            case \"compliant\":\n                return \"bg-dealverse-green/10 border-dealverse-green/20\";\n            case \"warning\":\n                return \"bg-dealverse-amber/10 border-dealverse-amber/20\";\n            case \"action_required\":\n                return \"bg-dealverse-coral/10 border-dealverse-coral/20\";\n            default:\n                return \"bg-gray-100 border-gray-200\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"compliant\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-dealverse-green\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 32\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-dealverse-amber\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 30\n                }, this);\n            case \"action_required\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-dealverse-coral\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 38\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-dealverse-medium-gray\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getRiskColor = (level)=>{\n        switch(level){\n            case \"high\":\n                return \"text-dealverse-coral\";\n            case \"medium\":\n                return \"text-dealverse-amber\";\n            case \"low\":\n                return \"text-dealverse-green\";\n            default:\n                return \"text-dealverse-medium-gray\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-dealverse-coral/10 text-dealverse-coral border-dealverse-coral/20\";\n            case \"medium\":\n                return \"bg-dealverse-amber/10 text-dealverse-amber border-dealverse-amber/20\";\n            case \"low\":\n                return \"bg-dealverse-green/10 text-dealverse-green border-dealverse-green/20\";\n            default:\n                return \"bg-gray-100 text-gray-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-6 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight bg-gradient-to-r from-dealverse-navy to-dealverse-blue bg-clip-text text-transparent\",\n                        children: \"Compliance Guardian\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-dealverse-medium-gray dark:text-dealverse-light-gray\",\n                        children: \"Regulatory compliance monitoring and audit trail management\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"border-0 bg-gradient-to-br from-dealverse-green/10 to-dealverse-green/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"text-sm font-medium text-dealverse-navy\",\n                                        children: \"Compliance Score\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-dealverse-green\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-dealverse-navy\",\n                                        children: \"92%\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-dealverse-medium-gray\",\n                                        children: \"Overall rating\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"border-0 bg-gradient-to-br from-dealverse-coral/10 to-dealverse-coral/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"text-sm font-medium text-dealverse-navy\",\n                                        children: \"Action Items\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-dealverse-coral\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-dealverse-navy\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-dealverse-medium-gray\",\n                                        children: \"Require attention\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"border-0 bg-gradient-to-br from-dealverse-blue/10 to-dealverse-blue/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"text-sm font-medium text-dealverse-navy\",\n                                        children: \"Audit Events\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-dealverse-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-dealverse-navy\",\n                                        children: \"1,247\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-dealverse-medium-gray\",\n                                        children: \"This month\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"border-0 bg-gradient-to-br from-dealverse-amber/10 to-dealverse-amber/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"text-sm font-medium text-dealverse-navy\",\n                                        children: \"Next Review\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 text-dealverse-amber\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-dealverse-navy\",\n                                        children: \"7\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-dealverse-medium-gray\",\n                                        children: \"days remaining\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                                            className: \"text-xl font-semibold text-dealverse-navy\",\n                                                            children: \"Compliance Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                                            className: \"text-dealverse-medium-gray\",\n                                                            children: \"Real-time compliance monitoring across all regulatory areas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: complianceMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                    className: `cursor-pointer transition-all duration-200 hover:shadow-md ${selectedMetric.category === metric.category ? \"ring-2 ring-dealverse-blue bg-dealverse-blue/5\" : \"hover:bg-dealverse-light-gray/50\"}`,\n                                                    onClick: ()=>setSelectedMetric(metric),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            getStatusIcon(metric.status),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-semibold text-dealverse-navy\",\n                                                                                        children: metric.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-dealverse-medium-gray\",\n                                                                                        children: [\n                                                                                            metric.completed,\n                                                                                            \"/\",\n                                                                                            metric.requirements,\n                                                                                            \" requirements met\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                        lineNumber: 285,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: `text-2xl font-bold ${getStatusColor(metric.status)}`,\n                                                                                children: [\n                                                                                    metric.score,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: `text-xs ${getStatusBg(metric.status)}`,\n                                                                                children: metric.status.replace(\"_\", \" \")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                                        value: metric.score,\n                                                                        className: \"h-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between text-xs text-dealverse-medium-gray mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Last review: \",\n                                                                                    metric.lastReview\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Next review: \",\n                                                                                    metric.nextReview\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                lineNumber: 303,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                                className: \"text-xl font-semibold text-dealverse-navy\",\n                                                children: \"Audit Trail\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                                className: \"text-dealverse-medium-gray\",\n                                                children: \"Complete audit trail of all compliance-related activities\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: auditTrail.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3 p-3 rounded-lg border border-dealverse-light-gray\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-2 h-2 rounded-full mt-2 ${event.riskLevel === \"high\" ? \"bg-dealverse-coral\" : event.riskLevel === \"medium\" ? \"bg-dealverse-amber\" : \"bg-dealverse-green\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-dealverse-navy\",\n                                                                            children: event.action\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: `text-xs ${event.riskLevel === \"high\" ? \"bg-dealverse-coral/10 text-dealverse-coral\" : event.riskLevel === \"medium\" ? \"bg-dealverse-amber/10 text-dealverse-amber\" : \"bg-dealverse-green/10 text-dealverse-green\"}`,\n                                                                            children: event.riskLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-dealverse-medium-gray mt-1\",\n                                                                    children: event.details\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 text-xs text-dealverse-medium-gray mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                event.user\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                new Date(event.timestamp).toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                event.category\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, event.id, true, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                                className: \"text-lg font-semibold text-dealverse-navy\",\n                                                children: \"Regulatory Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                                className: \"text-dealverse-medium-gray\",\n                                                children: \"Latest regulatory changes and notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: regulatoryUpdates.map((update, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-l-2 border-dealverse-blue/20 pl-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-sm text-dealverse-navy\",\n                                                                    children: update.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: `text-xs ${update.impact === \"High\" ? \"border-dealverse-coral text-dealverse-coral\" : update.impact === \"Medium\" ? \"border-dealverse-amber text-dealverse-amber\" : \"border-dealverse-green text-dealverse-green\"}`,\n                                                                    children: update.impact\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-dealverse-medium-gray mb-1\",\n                                                            children: update.summary\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-dealverse-blue\",\n                                                                    children: update.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-dealverse-medium-gray\",\n                                                                    children: update.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                                className: \"text-lg font-semibold text-dealverse-navy\",\n                                                children: \"Compliance Checklist\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                                className: \"text-dealverse-medium-gray\",\n                                                children: \"Upcoming compliance tasks and deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: complianceChecklist.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 rounded-lg bg-dealverse-light-gray/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                item.status === \"completed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-dealverse-green\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this) : item.status === \"in_progress\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-dealverse-amber\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-dealverse-coral\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-dealverse-navy\",\n                                                                            children: item.item\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-dealverse-medium-gray\",\n                                                                            children: [\n                                                                                \"Due: \",\n                                                                                item.dueDate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: `text-xs ${getPriorityColor(item.priority)}`,\n                                                            children: item.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"text-lg font-semibold text-dealverse-navy\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"w-full bg-dealverse-blue hover:bg-dealverse-blue/90\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Generate Report\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Export Audit Trail\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Set Reminder\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_Clock_Download_FileText_RefreshCw_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Configure Alerts\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/compliance/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/sidebar */ \"(ssr)/./components/sidebar.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/header */ \"(ssr)/./components/header.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const handleMobileMenuToggle = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const handleMobileMenuClose = ()=>{\n        setIsMobileMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex bg-dealverse-light-gray dark:bg-dealverse-dark-gray\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    isMobileMenuOpen: isMobileMenuOpen,\n                    onMobileMenuClose: handleMobileMenuClose\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-y-auto w-full md:w-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onMobileMenuToggle: handleMobileMenuToggle,\n                            isMobileMenuOpen: isMobileMenuOpen\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"p-unit-2 md:p-unit-4 lg:p-unit-6 bg-dealverse-light-gray dark:bg-dealverse-dark-gray min-h-[calc(100vh-4rem)] md:min-h-[calc(100vh-5rem)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.withAuth)(DashboardLayout));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZGFzaGJvYXJkL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0Y7QUFDSztBQUNiO0FBRWpDLFNBQVNJLGdCQUFnQixFQUN2QkMsUUFBUSxFQUdUO0lBQ0MsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHSiwrQ0FBUUEsQ0FBQztJQUV6RCxNQUFNSyx5QkFBeUI7UUFDN0JELG9CQUFvQixDQUFDRDtJQUN2QjtJQUVBLE1BQU1HLHdCQUF3QjtRQUM1QkYsb0JBQW9CO0lBQ3RCO0lBRUEscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDWCwyREFBT0E7b0JBQ05NLGtCQUFrQkE7b0JBQ2xCTSxtQkFBbUJIOzs7Ozs7OEJBSXJCLDhEQUFDQztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNWLDBEQUFNQTs0QkFDTFksb0JBQW9CTDs0QkFDcEJGLGtCQUFrQkE7Ozs7OztzQ0FFcEIsOERBQUNROzRCQUFLSCxXQUFVO3NDQUNkLDRFQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWk47Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPZjtBQUVBLGlFQUFlSCwyREFBUUEsQ0FBQ0UsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4PzcxZTYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBTaWRlYmFyIGZyb20gXCJAL2NvbXBvbmVudHMvc2lkZWJhclwiO1xuaW1wb3J0IEhlYWRlciBmcm9tIFwiQC9jb21wb25lbnRzL2hlYWRlclwiO1xuaW1wb3J0IHsgd2l0aEF1dGggfSBmcm9tIFwiQC9saWIvYXV0aC1jb250ZXh0XCI7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuXG5mdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgY29uc3QgW2lzTW9iaWxlTWVudU9wZW4sIHNldElzTW9iaWxlTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGhhbmRsZU1vYmlsZU1lbnVUb2dnbGUgPSAoKSA9PiB7XG4gICAgc2V0SXNNb2JpbGVNZW51T3BlbighaXNNb2JpbGVNZW51T3Blbik7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTW9iaWxlTWVudUNsb3NlID0gKCkgPT4ge1xuICAgIHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgbWluLWgtc2NyZWVuXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGJnLWRlYWx2ZXJzZS1saWdodC1ncmF5IGRhcms6YmctZGVhbHZlcnNlLWRhcmstZ3JheVwiPlxuICAgICAgICB7LyogU2lkZWJhciAtIEhpZGRlbiBvbiBtb2JpbGUsIHNob3duIGFzIG92ZXJsYXkgd2hlbiBtZW51IGlzIG9wZW4gKi99XG4gICAgICAgIDxTaWRlYmFyXG4gICAgICAgICAgaXNNb2JpbGVNZW51T3Blbj17aXNNb2JpbGVNZW51T3Blbn1cbiAgICAgICAgICBvbk1vYmlsZU1lbnVDbG9zZT17aGFuZGxlTW9iaWxlTWVudUNsb3NlfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGgtZnVsbCBvdmVyZmxvdy15LWF1dG8gdy1mdWxsIG1kOnctYXV0b1wiPlxuICAgICAgICAgIDxIZWFkZXJcbiAgICAgICAgICAgIG9uTW9iaWxlTWVudVRvZ2dsZT17aGFuZGxlTW9iaWxlTWVudVRvZ2dsZX1cbiAgICAgICAgICAgIGlzTW9iaWxlTWVudU9wZW49e2lzTW9iaWxlTWVudU9wZW59XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJwLXVuaXQtMiBtZDpwLXVuaXQtNCBsZzpwLXVuaXQtNiBiZy1kZWFsdmVyc2UtbGlnaHQtZ3JheSBkYXJrOmJnLWRlYWx2ZXJzZS1kYXJrLWdyYXkgbWluLWgtW2NhbGMoMTAwdmgtNHJlbSldIG1kOm1pbi1oLVtjYWxjKDEwMHZoLTVyZW0pXVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHdpdGhBdXRoKERhc2hib2FyZExheW91dCk7Il0sIm5hbWVzIjpbIlNpZGViYXIiLCJIZWFkZXIiLCJ3aXRoQXV0aCIsInVzZVN0YXRlIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJpc01vYmlsZU1lbnVPcGVuIiwic2V0SXNNb2JpbGVNZW51T3BlbiIsImhhbmRsZU1vYmlsZU1lbnVUb2dnbGUiLCJoYW5kbGVNb2JpbGVNZW51Q2xvc2UiLCJkaXYiLCJjbGFzc05hbWUiLCJvbk1vYmlsZU1lbnVDbG9zZSIsIm9uTW9iaWxlTWVudVRvZ2dsZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_mode_toggle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/mode-toggle */ \"(ssr)/./components/mode-toggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Search,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Search,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Search,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Search,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Search,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Temporarily disabled Clerk UserButton for development\n// import { UserButton } from \"@clerk/nextjs\";\n\n\n\n\n\nfunction Header({ onMobileMenuToggle, isMobileMenuOpen }) {\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-unit-2 md:px-unit-4 py-unit-2 border-b border-dealverse-blue/10 h-16 md:h-20 flex items-center justify-between bg-white/50 dark:bg-dealverse-navy/50 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-unit-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"md:hidden hover:bg-dealverse-blue/10 dealverse-focus\",\n                        onClick: onMobileMenuToggle,\n                        \"aria-label\": \"Toggle mobile menu\",\n                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-dealverse-medium-gray\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5 text-dealverse-medium-gray\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-dealverse-blue to-dealverse-green rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"DV\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex flex-1 max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dealverse-medium-gray\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            placeholder: \"Search deals, clients, documents...\",\n                            className: \"pl-10 bg-white dark:bg-dealverse-navy border-dealverse-blue/20 focus:border-dealverse-blue focus:ring-dealverse-blue/20 dealverse-focus\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    className: \"hover:bg-dealverse-blue/10 dealverse-focus\",\n                    onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                    \"aria-label\": \"Toggle search\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-5 w-5 text-dealverse-medium-gray\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-x-unit-1 md:gap-x-unit-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"relative hover:bg-dealverse-blue/10 dealverse-focus\",\n                        \"aria-label\": \"Notifications\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 md:h-5 md:w-5 text-dealverse-medium-gray\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -top-1 -right-1 h-2 w-2 md:h-3 md:w-3 bg-dealverse-coral rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"hidden sm:flex hover:bg-dealverse-blue/10 dealverse-focus\",\n                        \"aria-label\": \"Settings\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-5 w-5 text-dealverse-medium-gray\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_1__.ModeToggle, {}, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-dealverse-blue to-dealverse-green rounded-full flex items-center justify-center text-white font-semibold shadow-lg hover:scale-105 transition-transform duration-200 cursor-pointer dealverse-focus\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs md:text-sm\",\n                            children: \"U\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 p-unit-2 bg-white dark:bg-dealverse-navy border-b border-dealverse-blue/10 md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Search_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dealverse-medium-gray\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            placeholder: \"Search deals, clients, documents...\",\n                            className: \"pl-10 bg-white dark:bg-dealverse-navy border-dealverse-blue/20 focus:border-dealverse-blue focus:ring-dealverse-blue/20 dealverse-focus\",\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\header.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/mode-toggle.tsx":
/*!************************************!*\
  !*** ./components/mode-toggle.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModeToggle: () => (/* binding */ ModeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ ModeToggle auto */ \n\n\n\n\n\nfunction ModeToggle() {\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Toggle theme\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                align: \"end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"light\"),\n                        children: \"Light\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"dark\"),\n                        children: \"Dark\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"system\"),\n                        children: \"System\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\mode-toggle.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/mode-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-4.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calculator,FileText,LayoutDashboard,Presentation,Search,Settings,Shield,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst routes = [\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: \"Dashboard\",\n        href: \"/dashboard\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"Prospect AI\",\n        href: \"/dashboard/prospect-ai\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"Diligence Navigator\",\n        href: \"/dashboard/diligence\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Valuation Hub\",\n        href: \"/dashboard/valuation\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Compliance Guardian\",\n        href: \"/dashboard/compliance\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"PitchCraft Suite\",\n        href: \"/dashboard/pitchcraft\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: \"Deals\",\n        href: \"/dashboard/deals\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        label: \"Clients\",\n        href: \"/dashboard/clients\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: \"Settings\",\n        href: \"/dashboard/settings\"\n    }\n];\nfunction Sidebar({ isMobileMenuOpen, onMobileMenuClose }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden\",\n                onClick: onMobileMenuClose\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        h-full flex flex-col overflow-y-auto bg-dealverse-navy border-r border-dealverse-blue/10 shadow-2xl\n        fixed md:relative z-50 md:z-auto\n        w-64 md:w-64\n        transform transition-transform duration-300 ease-in-out\n        ${isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full md:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-unit-3 border-b border-dealverse-blue/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-unit-2 group dealverse-focus\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-dealverse-blue to-dealverse-green rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-base\",\n                                                    children: \"DV\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-bold text-h3 text-white group-hover:text-dealverse-blue transition-colors duration-300\",\n                                                children: \"DealVerse OS\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"md:hidden text-white hover:bg-dealverse-blue/20 dealverse-focus\",\n                                    onClick: onMobileMenuClose,\n                                    \"aria-label\": \"Close menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calculator_FileText_LayoutDashboard_Presentation_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col w-full pt-unit-3 px-unit-2 space-y-unit-1\",\n                        children: routes.map((route)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: route.href,\n                                className: `flex items-center gap-x-unit-2 text-sm font-medium p-unit-2 rounded-xl transition-all duration-300 group dealverse-focus ${pathname === route.href ? \"text-white bg-gradient-to-r from-dealverse-blue to-dealverse-blue/80 shadow-lg shadow-dealverse-blue/25\" : \"text-dealverse-light-gray hover:text-white hover:bg-dealverse-blue/10 hover:shadow-md\"}`,\n                                onClick: ()=>{\n                                    // Close mobile menu when navigation item is clicked\n                                    if (onMobileMenuClose) {\n                                        onMobileMenuClose();\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(route.icon, {\n                                        className: `h-5 w-5 transition-transform duration-300 ${pathname === route.href ? \"scale-110 text-white\" : \"group-hover:scale-110\"}`\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: route.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, route.href, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto p-unit-3 border-t border-dealverse-blue/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-caption text-dealverse-light-gray/60 text-center\",\n                            children: \"DealVerse OS v1.0\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\sidebar.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, defaultTheme = \"light\", enableSystem = true, disableTransitionOnChange = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: defaultTheme,\n        enableSystem: enableSystem,\n        disableTransitionOnChange: disableTransitionOnChange,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRTFELFNBQVNDLGNBQWMsRUFDNUJFLFFBQVEsRUFDUkMsZUFBZSxPQUFPLEVBQ3RCQyxlQUFlLElBQUksRUFDbkJDLDRCQUE0QixLQUFLLEVBTWxDO0lBQ0MscUJBQ0UsOERBQUNKLHNEQUFrQkE7UUFDakJLLFdBQVU7UUFDVkgsY0FBY0E7UUFDZEMsY0FBY0E7UUFDZEMsMkJBQTJCQTtrQkFFMUJIOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWx2ZXJzZS1vcy8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgZGVmYXVsdFRoZW1lID0gXCJsaWdodFwiLFxuICBlbmFibGVTeXN0ZW0gPSB0cnVlLFxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlID0gZmFsc2Vcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBkZWZhdWx0VGhlbWU/OiBzdHJpbmdcbiAgZW5hYmxlU3lzdGVtPzogYm9vbGVhblxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPzogYm9vbGVhblxufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0VGhlbWVzUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT17ZGVmYXVsdFRoZW1lfVxuICAgICAgZW5hYmxlU3lzdGVtPXtlbmFibGVTeXN0ZW19XG4gICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPXtkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRUaGVtZXNQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsImF0dHJpYnV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            // DealVerse OS Status Badges\n            success: \"border-transparent bg-dealverse-green text-white hover:bg-dealverse-green/80\",\n            warning: \"border-transparent bg-dealverse-amber text-white hover:bg-dealverse-amber/80\",\n            error: \"border-transparent bg-dealverse-coral text-white hover:bg-dealverse-coral/80\",\n            info: \"border-transparent bg-dealverse-blue text-white hover:bg-dealverse-blue/80\",\n            // Deal Status Badges\n            \"deal-active\": \"border-dealverse-green/20 bg-dealverse-green/10 text-dealverse-green hover:bg-dealverse-green/20\",\n            \"deal-pending\": \"border-dealverse-amber/20 bg-dealverse-amber/10 text-dealverse-amber hover:bg-dealverse-amber/20\",\n            \"deal-closed\": \"border-dealverse-navy/20 bg-dealverse-navy/10 text-dealverse-navy hover:bg-dealverse-navy/20\",\n            \"deal-cancelled\": \"border-dealverse-coral/20 bg-dealverse-coral/10 text-dealverse-coral hover:bg-dealverse-coral/20\",\n            // Risk Level Badges\n            \"risk-low\": \"border-dealverse-green/20 bg-dealverse-green/10 text-dealverse-green\",\n            \"risk-medium\": \"border-dealverse-amber/20 bg-dealverse-amber/10 text-dealverse-amber\",\n            \"risk-high\": \"border-dealverse-coral/20 bg-dealverse-coral/10 text-dealverse-coral\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-300 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-dealverse-blue focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-dealverse-blue text-white hover:bg-dealverse-blue/90 hover:shadow-lg hover:shadow-dealverse-blue/25 hover:scale-105\",\n            destructive: \"bg-dealverse-coral text-white hover:bg-dealverse-coral/90 hover:shadow-lg hover:shadow-dealverse-coral/25\",\n            outline: \"border-2 border-dealverse-blue bg-transparent text-dealverse-blue hover:bg-dealverse-blue hover:text-white hover:shadow-lg\",\n            secondary: \"bg-dealverse-navy text-white hover:bg-dealverse-navy/90 hover:shadow-lg\",\n            ghost: \"hover:bg-dealverse-blue/10 hover:text-dealverse-blue\",\n            link: \"text-dealverse-blue underline-offset-4 hover:underline hover:text-dealverse-blue/80\",\n            success: \"bg-dealverse-green text-white hover:bg-dealverse-green/90 hover:shadow-lg hover:shadow-dealverse-green/25\",\n            warning: \"bg-dealverse-amber text-white hover:bg-dealverse-amber/90 hover:shadow-lg hover:shadow-dealverse-amber/25\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-lg px-3\",\n            lg: \"h-12 rounded-lg px-8 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-300 ease-in-out hover:shadow-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLG9IQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUM5QyxHQUFHQyxLQUFLOzs7Ozs7QUFHYkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNLO1FBQ0NMLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCWiw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNPO1FBQ0NQLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjZCw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFmLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vY29tcG9uZW50cy91aS9jYXJkLnRzeD9hZDkxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgQ2FyZCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJvdW5kZWQteGwgYm9yZGVyIGJnLWNhcmQgdGV4dC1jYXJkLWZvcmVncm91bmQgc2hhZG93LXNtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBob3ZlcjpzaGFkb3ctbWRcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuQ2FyZENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIkNhcmRDb250ZW50XCJcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGl0ZW1zLWNlbnRlciBwLTYgcHQtMFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkRm9vdGVyXCJcblxuZXhwb3J0IHsgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZEZvb3RlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/progress.tsx":
/*!************************************!*\
  !*** ./components/ui/progress.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/**\n * API Client for DealVerse OS Backend\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.token = null;\n        this.baseUrl = baseUrl;\n        // Get token from localStorage if available\n        if (false) {}\n    }\n    setToken(token) {\n        this.token = token;\n        if (false) {}\n    }\n    clearToken() {\n        this.token = null;\n        if (false) {}\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...options.headers || {}\n        };\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token expired, try to refresh\n                    const refreshed = await this.refreshToken();\n                    if (refreshed) {\n                        // Retry the request with new token\n                        headers.Authorization = `Bearer ${this.token}`;\n                        const retryResponse = await fetch(url, {\n                            ...options,\n                            headers\n                        });\n                        if (retryResponse.ok) {\n                            const data = await retryResponse.json();\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // Refresh failed, clear tokens and redirect to login\n                    this.clearToken();\n                    if (false) {}\n                    throw new Error(\"Authentication failed\");\n                }\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            // Check if it's a network error\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                return {\n                    error: \"Unable to connect to server. Please check your connection and try again.\"\n                };\n            }\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async refreshToken() {\n        const refreshToken =  false ? 0 : null;\n        if (!refreshToken) return false;\n        try {\n            const response = await fetch(`${this.baseUrl}/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                this.setToken(data.access_token);\n                if (false) {}\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Token refresh failed:\", error);\n        }\n        return false;\n    }\n    // Authentication endpoints\n    async login(email, password) {\n        return this.request(\"/auth/login/json\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    }\n    async register(userData) {\n        return this.request(\"/auth/register\", {\n            method: \"POST\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async logout() {\n        const result = await this.request(\"/auth/logout\", {\n            method: \"POST\"\n        });\n        this.clearToken();\n        return result;\n    }\n    // User endpoints\n    async getCurrentUser() {\n        return this.request(\"/users/me\");\n    }\n    async updateCurrentUser(userData) {\n        return this.request(\"/users/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async getUsers(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/users?${query.toString()}`);\n    }\n    // Deal endpoints\n    async getDeals(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.stage) query.append(\"stage\", params.stage);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/deals?${query.toString()}`);\n    }\n    async getDeal(id) {\n        return this.request(`/deals/${id}`);\n    }\n    async createDeal(dealData) {\n        return this.request(\"/deals\", {\n            method: \"POST\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async updateDeal(id, dealData) {\n        return this.request(`/deals/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async deleteDeal(id) {\n        return this.request(`/deals/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async getDealsStats() {\n        return this.request(\"/deals/stats/summary\");\n    }\n    // Client endpoints\n    async getClients(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.client_type) query.append(\"client_type\", params.client_type);\n        if (params?.industry) query.append(\"industry\", params.industry);\n        if (params?.search) query.append(\"search\", params.search);\n        return this.request(`/clients?${query.toString()}`);\n    }\n    async getClient(id) {\n        return this.request(`/clients/${id}`);\n    }\n    async createClient(clientData) {\n        return this.request(\"/clients\", {\n            method: \"POST\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async updateClient(id, clientData) {\n        return this.request(`/clients/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async deleteClient(id) {\n        return this.request(`/clients/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Analytics endpoints\n    async getDashboardAnalytics() {\n        return this.request(\"/analytics/dashboard\");\n    }\n    async getDealsPerformance(days = 30) {\n        return this.request(`/analytics/deals/performance?days=${days}`);\n    }\n    async getClientInsights() {\n        return this.request(\"/analytics/clients/insights\");\n    }\n    async getTeamProductivity() {\n        return this.request(\"/analytics/team/productivity\");\n    }\n    // Document endpoints\n    async getDocuments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.document_type) query.append(\"document_type\", params.document_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/documents?${query.toString()}`);\n    }\n    async getDocument(id) {\n        return this.request(`/documents/${id}`);\n    }\n    async createDocument(documentData) {\n        return this.request(\"/documents\", {\n            method: \"POST\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async uploadDocument(file, metadata) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        formData.append(\"title\", metadata.title);\n        if (metadata.document_type) formData.append(\"document_type\", metadata.document_type);\n        if (metadata.deal_id) formData.append(\"deal_id\", metadata.deal_id);\n        if (metadata.client_id) formData.append(\"client_id\", metadata.client_id);\n        if (metadata.is_confidential !== undefined) formData.append(\"is_confidential\", metadata.is_confidential.toString());\n        const headers = {};\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(`${this.baseUrl}/documents/upload`, {\n                method: \"POST\",\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"Document upload failed:\", error);\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async updateDocument(id, documentData) {\n        return this.request(`/documents/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async deleteDocument(id) {\n        return this.request(`/documents/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Document Analysis endpoints\n    async analyzeDocument(id, analysisType, focusAreas) {\n        return this.request(`/documents/${id}/analyze`, {\n            method: \"POST\",\n            body: JSON.stringify({\n                analysis_type: analysisType || \"full\",\n                focus_areas: focusAreas || [\n                    \"financial\",\n                    \"legal\",\n                    \"risk\"\n                ]\n            })\n        });\n    }\n    async getDocumentAnalysis(id) {\n        return this.request(`/documents/${id}/analysis`);\n    }\n    async assessDocumentRisk(documentIds, assessmentType) {\n        return this.request(\"/documents/risk-assessment\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                document_ids: documentIds,\n                assessment_type: assessmentType || \"comprehensive\"\n            })\n        });\n    }\n    async getRiskAssessments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/documents/risk-assessments?${query}`);\n    }\n    async categorizeDocument(id) {\n        return this.request(`/documents/${id}/categorize`);\n    }\n    async getDocumentAnalytics() {\n        return this.request(\"/documents/analytics/statistics\");\n    }\n    async getHighRiskDocuments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.risk_threshold) query.append(\"risk_threshold\", params.risk_threshold.toString());\n        return this.request(`/documents/high-risk?${query}`);\n    }\n    // Financial Models endpoints\n    async getFinancialModels(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.model_type) query.append(\"model_type\", params.model_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/financial-models?${query.toString()}`);\n    }\n    async getFinancialModel(id) {\n        return this.request(`/financial-models/${id}`);\n    }\n    async createFinancialModel(modelData) {\n        return this.request(\"/financial-models\", {\n            method: \"POST\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async updateFinancialModel(id, modelData) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async deleteFinancialModel(id) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createModelVersion(id, versionData) {\n        return this.request(`/financial-models/${id}/versions`, {\n            method: \"POST\",\n            body: JSON.stringify(versionData)\n        });\n    }\n    async getModelVersions(id) {\n        return this.request(`/financial-models/${id}/versions`);\n    }\n    async getModelStatistics() {\n        return this.request(\"/financial-models/statistics\");\n    }\n    // Organization endpoints\n    async getCurrentOrganization() {\n        return this.request(\"/organizations/me\");\n    }\n    async updateCurrentOrganization(orgData) {\n        return this.request(\"/organizations/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(orgData)\n        });\n    }\n    async getOrganizationStats() {\n        return this.request(\"/organizations/me/stats\");\n    }\n    // Presentation endpoints\n    async getPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.status) query.append(\"status\", params.status);\n        if (params?.presentation_type) query.append(\"presentation_type\", params.presentation_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.created_by_me) query.append(\"created_by_me\", params.created_by_me.toString());\n        return this.request(`/presentations/?${query.toString()}`);\n    }\n    async getPresentation(id) {\n        return this.request(`/presentations/${id}`);\n    }\n    async createPresentation(presentationData) {\n        return this.request(\"/presentations/\", {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async updatePresentation(id, presentationData) {\n        return this.request(`/presentations/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async deletePresentation(id) {\n        return this.request(`/presentations/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createPresentationVersion(id) {\n        return this.request(`/presentations/${id}/version`, {\n            method: \"POST\"\n        });\n    }\n    async getPresentationsByDeal(dealId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/deals/${dealId}?${query.toString()}`);\n    }\n    async getSharedPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/shared?${query.toString()}`);\n    }\n    // Slide endpoints\n    async getPresentationSlides(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/slides?${query.toString()}`);\n    }\n    async createSlide(presentationId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/`, {\n            method: \"POST\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async getSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`);\n    }\n    async updateSlide(presentationId, slideId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"PUT\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async deleteSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async duplicateSlide(presentationId, slideId, newSlideNumber) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}/duplicate?new_slide_number=${newSlideNumber}`, {\n            method: \"POST\"\n        });\n    }\n    // Template endpoints\n    async getPresentationTemplates(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.category) query.append(\"category\", params.category);\n        if (params?.featured_only) query.append(\"featured_only\", params.featured_only.toString());\n        if (params?.public_only) query.append(\"public_only\", params.public_only.toString());\n        return this.request(`/presentations/templates/?${query.toString()}`);\n    }\n    async createPresentationTemplate(templateData) {\n        return this.request(\"/presentations/templates/\", {\n            method: \"POST\",\n            body: JSON.stringify(templateData)\n        });\n    }\n    async getPresentationTemplate(id) {\n        return this.request(`/presentations/templates/${id}`);\n    }\n    async createPresentationFromTemplate(templateId, presentationData) {\n        return this.request(`/presentations/templates/${templateId}/use`, {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    // Comment endpoints\n    async getPresentationComments(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.resolved_only !== undefined) query.append(\"resolved_only\", params.resolved_only.toString());\n        return this.request(`/presentations/${presentationId}/comments?${query.toString()}`);\n    }\n    async createPresentationComment(presentationId, commentData) {\n        return this.request(`/presentations/${presentationId}/comments/`, {\n            method: \"POST\",\n            body: JSON.stringify(commentData)\n        });\n    }\n    async resolvePresentationComment(presentationId, commentId) {\n        return this.request(`/presentations/${presentationId}/comments/${commentId}/resolve`, {\n            method: \"PUT\"\n        });\n    }\n    // Collaboration endpoints\n    async getPresentationActivities(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/activities?${query.toString()}`);\n    }\n    async getPresentationActiveUsers(presentationId) {\n        return this.request(`/presentations/${presentationId}/active-users`);\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is already logged in\n        const token = localStorage.getItem(\"access_token\");\n        if (token) {\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(token);\n            refreshUser();\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const refreshUser = async ()=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.getCurrentUser();\n            if (response.data) {\n                setUser(response.data);\n            } else {\n                // Token is invalid, clear it\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.login(email, password);\n            if (response.data) {\n                const { access_token, refresh_token } = response.data;\n                // Store tokens\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(access_token);\n                localStorage.setItem(\"refresh_token\", refresh_token);\n                // Get user data\n                await refreshUser();\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Login failed\"\n            };\n        }\n    };\n    const logout = ()=>{\n        _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.logout();\n        setUser(null);\n        // Redirect to login page\n        window.location.href = \"/auth/login\";\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protecting routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login\n            if (false) {}\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37beebff4f30\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvLi9hcHAvZ2xvYmFscy5jc3M/NmZkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3YmVlYmZmNGYzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/compliance/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/compliance/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\app\dashboard\compliance\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\app\dashboard\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"DealVerse OS - Investment Banking Platform\",\n    description: \"AI-powered investment banking platform with deal sourcing, due diligence, valuation modeling, compliance management, and presentation tools.\",\n    keywords: \"investment banking, deal sourcing, due diligence, valuation, compliance, AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#withAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/@floating-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/tslib","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();