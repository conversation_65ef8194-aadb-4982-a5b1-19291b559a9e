"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  FileText,
  FolderOpen,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Upload,
  Shield,
  AlertCircle,
  TrendingUp,
  FileCheck,
  XCircle,
  Loader2,
  RefreshCw
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { ChevronRight, ChevronDown } from "lucide-react";
import { useDocuments, type Document, type DocumentTreeNode } from "@/hooks/use-documents";
import { useAuth } from "@/lib/auth-context";
import { DocumentPreview } from "@/components/dashboard/document-preview";
import { AIDocumentAnalysis } from "@/components/dashboard/ai-document-analysis";
import { useDocumentAnalysis } from "@/hooks/use-document-analysis";

// Enhanced document tree with progress indicators
const documentTree = [
  {
    id: "1",
    name: "Financial Documents",
    type: "folder",
    progress: 75,
    totalFiles: 8,
    completedFiles: 6,
    children: [
      {
        id: "1-1",
        name: "Audited Financial Statements (2021-2023)",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "2.4 MB",
        lastModified: "2 days ago",
        annotations: 3
      },
      {
        id: "1-2",
        name: "Management Accounts Q1-Q3 2024",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "1.8 MB",
        lastModified: "1 day ago",
        annotations: 1
      },
      {
        id: "1-3",
        name: "Cash Flow Projections",
        type: "file",
        status: "review",
        riskLevel: "medium",
        size: "856 KB",
        lastModified: "3 hours ago",
        annotations: 5
      },
      {
        id: "1-4",
        name: "Debt Schedule",
        type: "file",
        status: "missing",
        riskLevel: "high",
        size: "N/A",
        lastModified: "N/A",
        annotations: 0
      },
      {
        id: "1-5",
        name: "Tax Returns (2021-2023)",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "3.2 MB",
        lastModified: "1 week ago",
        annotations: 2
      },
      {
        id: "1-6",
        name: "Budget vs Actual Analysis",
        type: "file",
        status: "review",
        riskLevel: "medium",
        size: "1.1 MB",
        lastModified: "2 days ago",
        annotations: 4
      },
      {
        id: "1-7",
        name: "Working Capital Analysis",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "945 KB",
        lastModified: "4 days ago",
        annotations: 2
      },
      {
        id: "1-8",
        name: "Revenue Recognition Policy",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "567 KB",
        lastModified: "1 week ago",
        annotations: 1
      }
    ]
  },
  {
    id: "2",
    name: "Legal Documents",
    type: "folder",
    progress: 60,
    totalFiles: 6,
    completedFiles: 3,
    children: [
      {
        id: "2-1",
        name: "Articles of Incorporation",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "234 KB",
        lastModified: "2 weeks ago",
        annotations: 0
      },
      {
        id: "2-2",
        name: "Material Contracts",
        type: "file",
        status: "review",
        riskLevel: "medium",
        size: "4.7 MB",
        lastModified: "1 day ago",
        annotations: 8
      },
      {
        id: "2-3",
        name: "Litigation Summary",
        type: "file",
        status: "flagged",
        riskLevel: "high",
        size: "1.3 MB",
        lastModified: "3 hours ago",
        annotations: 12
      },
      {
        id: "2-4",
        name: "IP Portfolio",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "2.1 MB",
        lastModified: "5 days ago",
        annotations: 3
      },
      {
        id: "2-5",
        name: "Employment Agreements",
        type: "file",
        status: "review",
        riskLevel: "medium",
        size: "1.8 MB",
        lastModified: "2 days ago",
        annotations: 6
      },
      {
        id: "2-6",
        name: "Regulatory Compliance",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "987 KB",
        lastModified: "1 week ago",
        annotations: 2
      }
    ]
  },
  {
    id: "3",
    name: "Commercial Documents",
    type: "folder",
    progress: 85,
    totalFiles: 5,
    completedFiles: 4,
    children: [
      {
        id: "3-1",
        name: "Customer Contracts",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "3.4 MB",
        lastModified: "3 days ago",
        annotations: 4
      },
      {
        id: "3-2",
        name: "Supplier Agreements",
        type: "file",
        status: "review",
        riskLevel: "medium",
        size: "2.2 MB",
        lastModified: "1 day ago",
        annotations: 7
      },
      {
        id: "3-3",
        name: "Market Analysis",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "1.6 MB",
        lastModified: "2 days ago",
        annotations: 2
      },
      {
        id: "3-4",
        name: "Competitive Landscape",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "1.9 MB",
        lastModified: "4 days ago",
        annotations: 3
      },
      {
        id: "3-5",
        name: "Sales Pipeline Analysis",
        type: "file",
        status: "complete",
        riskLevel: "low",
        size: "1.1 MB",
        lastModified: "1 week ago",
        annotations: 1
      }
    ]
  }
];

const riskAssessment = [
  {
    category: "Financial Risk",
    level: "Medium",
    score: 65,
    findings: [
      "Declining gross margins in Q3 2024",
      "High customer concentration (top 3 = 45%)",
      "Working capital requirements increasing"
    ]
  },
  {
    category: "Legal Risk", 
    level: "High",
    score: 85,
    findings: [
      "Ongoing patent litigation with competitor",
      "Regulatory compliance gaps identified",
      "Key contracts expiring within 12 months"
    ]
  },
  {
    category: "Operational Risk",
    level: "Low", 
    score: 25,
    findings: [
      "Strong management team",
      "Diversified supplier base",
      "Robust IT infrastructure"
    ]
  }
];

const missingDocuments = [
  "Debt Schedule and Covenant Details",
  "Environmental Impact Assessment", 
  "Employee Stock Option Plan",
  "Insurance Coverage Summary",
  "Tax Compliance Certificates"
];

export default function DiligencePage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [analyzingDocuments, setAnalyzingDocuments] = useState<Set<string>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Real AI analytics integration
  const {
    analytics,
    fetchAnalytics,
    getHighRiskDocuments,
    loading: analyticsLoading
  } = useDocumentAnalysis();

  // Use the documents hook to fetch real data
  const {
    documents,
    documentTree,
    loading,
    error,
    uploadProgress,
    fetchDocuments,
    uploadDocument,
    analyzeDocument,
    updateDocument,
    deleteDocument,
  } = useDocuments({ autoFetch: true });

  // Fetch analytics on component mount
  useEffect(() => {
    fetchAnalytics().catch(console.error);
  }, [fetchAnalytics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "complete": return <CheckCircle className="h-4 w-4 text-dealverse-green" />;
      case "review": return <Clock className="h-4 w-4 text-dealverse-amber" />;
      case "flagged": return <AlertTriangle className="h-4 w-4 text-dealverse-coral" />;
      case "missing": return <XCircle className="h-4 w-4 text-dealverse-coral" />;
      default: return <FileText className="h-4 w-4 text-dealverse-medium-gray" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const styles: Record<string, string> = {
      complete: "bg-dealverse-green/10 text-dealverse-green border-dealverse-green/20",
      review: "bg-dealverse-amber/10 text-dealverse-amber border-dealverse-amber/20",
      flagged: "bg-dealverse-coral/10 text-dealverse-coral border-dealverse-coral/20",
      missing: "bg-dealverse-coral/10 text-dealverse-coral border-dealverse-coral/20"
    };
    return styles[status] || "bg-gray-100 text-gray-600";
  };

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "high": return "text-dealverse-coral";
      case "medium": return "text-dealverse-amber"; 
      case "low": return "text-dealverse-green";
      default: return "text-dealverse-medium-gray";
    }
  };

  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev =>
      prev.includes(folderId)
        ? prev.filter(id => id !== folderId)
        : [...prev, folderId]
    );
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      for (const file of Array.from(files)) {
        await uploadDocument(file, {
          title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
          document_type: "general",
          is_confidential: false,
        });
      }
    } catch (err) {
      console.error('Upload failed:', err);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleAnalyzeDocument = async (documentId: string) => {
    setAnalyzingDocuments(prev => new Set(prev).add(documentId));
    try {
      await analyzeDocument(documentId);
    } catch (err) {
      console.error('Analysis failed:', err);
    } finally {
      setAnalyzingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(documentId);
        return newSet;
      });
    }
  };

  // Calculate dashboard statistics from real data and AI analytics
  const stats = {
    documentsReviewed: analytics?.analyzed_documents || documents.filter(doc =>
      doc.status === 'analyzed' || doc.status === 'approved'
    ).length,
    totalDocuments: analytics?.total_documents || documents.length,
    riskFlags: analytics?.high_risk_documents || documents.filter(doc =>
      doc.risk_score && parseInt(doc.risk_score) > 70
    ).length,
    missingDocs: analytics?.pending_analysis || 5,
    completionPercentage: analytics?.analysis_completion_rate
      ? Math.round(analytics.analysis_completion_rate * 100)
      : (documents.length > 0
          ? Math.round((documents.filter(doc =>
              doc.status === 'analyzed' || doc.status === 'approved'
            ).length / documents.length) * 100)
          : 0),
  };

  // Filter documents based on search term
  const filteredTree = documentTree.map(folder => ({
    ...folder,
    children: folder.children?.filter(file =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase())
    ) || []
  })).filter(folder =>
    folder.children.length > 0 || searchTerm === ""
  );

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-dealverse-navy to-dealverse-blue bg-clip-text text-transparent">
          Diligence Navigator
        </h1>
        <p className="text-dealverse-medium-gray dark:text-dealverse-light-gray">
          AI-powered due diligence and document analysis
        </p>
      </div>

      {/* Due Diligence Dashboard */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-dealverse-blue/10 to-dealverse-blue/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-dealverse-navy">Documents Reviewed</CardTitle>
            <FileCheck className="h-4 w-4 text-dealverse-blue" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-dealverse-navy">
              {(loading || analyticsLoading) ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.documentsReviewed}
            </div>
            <p className="text-xs text-dealverse-medium-gray">of {stats.totalDocuments} total</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-dealverse-coral/10 to-dealverse-coral/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-dealverse-navy">Risk Flags</CardTitle>
            <AlertTriangle className="h-4 w-4 text-dealverse-coral" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-dealverse-navy">
              {(loading || analyticsLoading) ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.riskFlags}
            </div>
            <p className="text-xs text-dealverse-medium-gray">Require attention</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-dealverse-amber/10 to-dealverse-amber/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-dealverse-navy">Missing Docs</CardTitle>
            <XCircle className="h-4 w-4 text-dealverse-amber" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-dealverse-navy">
              {(loading || analyticsLoading) ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.missingDocs}
            </div>
            <p className="text-xs text-dealverse-medium-gray">Critical items</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-dealverse-green/10 to-dealverse-green/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-dealverse-navy">Completion</CardTitle>
            <TrendingUp className="h-4 w-4 text-dealverse-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-dealverse-navy">
              {(loading || analyticsLoading) ? <Loader2 className="h-6 w-6 animate-spin" /> : `${stats.completionPercentage}%`}
            </div>
            <p className="text-xs text-dealverse-medium-gray">Overall progress</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-5">
        {/* Document Tree */}
        <div className="lg:col-span-3">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-semibold text-dealverse-navy">Document Repository</CardTitle>
                  <CardDescription className="text-dealverse-medium-gray">
                    Organized document hierarchy with AI-powered analysis
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4 mr-2" />
                    )}
                    Upload
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchDocuments}
                    disabled={loading}
                  >
                    {loading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-2" />
                    )}
                    Refresh
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Search */}
              <div className="relative mb-6">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dealverse-medium-gray" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Enhanced Document Tree */}
              {error && (
                <div className="mb-4 p-3 bg-dealverse-coral/10 border border-dealverse-coral/20 rounded-lg">
                  <p className="text-sm text-dealverse-coral">{error}</p>
                </div>
              )}

              {loading && documents.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-dealverse-blue" />
                  <span className="ml-2 text-dealverse-medium-gray">Loading documents...</span>
                </div>
              ) : filteredTree.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-dealverse-medium-gray mx-auto mb-4" />
                  <p className="text-dealverse-medium-gray">
                    {searchTerm ? 'No documents match your search.' : 'No documents uploaded yet.'}
                  </p>
                  {!searchTerm && (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload First Document
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-unit-2">
                  {filteredTree.map((folder) => (
                  <div key={folder.id} className="dealverse-card border border-dealverse-blue/10 rounded-lg">
                    {/* Enhanced Folder Header */}
                    <div
                      className="flex items-center justify-between p-unit-2 hover:bg-dealverse-light-gray/50 cursor-pointer transition-colors duration-200"
                      onClick={() => toggleFolder(folder.id)}
                    >
                      <div className="flex items-center space-x-unit-2">
                        {expandedFolders.includes(folder.id) ? (
                          <ChevronDown className="h-4 w-4 text-dealverse-blue" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-dealverse-blue" />
                        )}
                        <FolderOpen className="h-5 w-5 text-dealverse-blue" />
                        <div className="flex flex-col">
                          <span className="font-semibold text-dealverse-navy dark:text-white">
                            {folder.name}
                          </span>
                          <span className="text-caption text-dealverse-medium-gray">
                            {folder.completedFiles}/{folder.totalFiles} files completed
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-unit-2">
                        <div className="flex flex-col items-end">
                          <span className="text-caption font-mono font-semibold text-dealverse-navy dark:text-white">
                            {folder.progress}%
                          </span>
                          <Progress value={folder.progress} className="h-1 w-16" />
                        </div>
                        <Badge variant="outline" className="text-caption">
                          {folder.children.length} items
                        </Badge>
                      </div>
                    </div>

                    {/* Enhanced Folder Contents */}
                    {expandedFolders.includes(folder.id) && (
                      <div className="border-t border-dealverse-blue/10">
                        <div className="p-unit-1 space-y-1">
                          {folder.children.map((file) => (
                            <div
                              key={file.id}
                              className="flex items-center justify-between p-unit-2 rounded-lg hover:bg-dealverse-light-gray/30 cursor-pointer transition-colors duration-200 group"
                              onClick={() => setSelectedDocument(file.document || null)}
                            >
                              <div className="flex items-center space-x-unit-2 flex-1">
                                {getStatusIcon(file.status || 'uploaded')}
                                <div className="flex flex-col flex-1">
                                  <span className="text-body text-dealverse-navy dark:text-white group-hover:text-dealverse-blue">
                                    {file.name}
                                  </span>
                                  <div className="flex items-center space-x-unit-2 text-caption text-dealverse-medium-gray">
                                    <span>{file.size}</span>
                                    <span>•</span>
                                    <span>{file.lastModified}</span>
                                    {file.annotations && file.annotations > 0 && (
                                      <>
                                        <span>•</span>
                                        <span className="text-dealverse-blue">
                                          {file.annotations} annotations
                                        </span>
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-unit-1">
                                <Badge
                                  variant={
                                    file.riskLevel === "high" ? "risk-high" :
                                    file.riskLevel === "medium" ? "risk-medium" : "risk-low"
                                  }
                                  className="text-caption"
                                >
                                  {file.riskLevel || 'low'} risk
                                </Badge>
                                <Badge
                                  variant={
                                    file.status === "analyzed" || file.status === "approved" ? "deal-closed" :
                                    file.status === "processing" || file.status === "uploaded" ? "deal-pending" :
                                    "deal-cancelled"
                                  }
                                  className="text-caption"
                                >
                                  {file.status || 'uploaded'}
                                </Badge>
                                <div className="flex space-x-1">
                                  {file.document && file.status !== 'analyzed' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAnalyzeDocument(file.document!.id);
                                      }}
                                    >
                                      <Shield className="h-3 w-3" />
                                    </Button>
                                  )}
                                  <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Eye className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Document Analysis Panel */}
        <div className="lg:col-span-2 space-y-6">
          {selectedDocument ? (
            <AIDocumentAnalysis
              document={selectedDocument}
              onAnalysisComplete={(result) => {
                // Update document status and refresh data
                console.log('Analysis completed:', result);
                // Optionally refresh documents list
                fetchDocuments();
              }}
            />
          ) : (
            <Card className="border-0 shadow-lg">
              <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                <FileText className="h-12 w-12 text-dealverse-medium-gray mb-4" />
                <h3 className="text-lg font-semibold text-dealverse-navy mb-2">
                  Select a Document
                </h3>
                <p className="text-dealverse-medium-gray">
                  Choose a document from the repository to view AI-powered analysis and insights.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Risk Assessment & Missing Documents - Moved to bottom */}
      <div className="grid gap-6 lg:grid-cols-2">
        <div className="space-y-6">
          {/* Risk Assessment */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-dealverse-navy">Risk Assessment</CardTitle>
              <CardDescription className="text-dealverse-medium-gray">
                AI-powered risk analysis and flagging
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-dealverse-blue" />
                </div>
              ) : (
                riskAssessment.map((risk, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-dealverse-navy">{risk.category}</h5>
                      <Badge className={`${getRiskColor(risk.level)} border-current`} variant="outline">
                        {risk.level}
                      </Badge>
                    </div>
                    <Progress value={risk.score} className="h-2" />
                    <div className="space-y-1">
                      {risk.findings.map((finding, findingIndex) => (
                        <div key={findingIndex} className="flex items-start text-xs">
                          <AlertCircle className="h-3 w-3 text-dealverse-coral mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-dealverse-medium-gray">{finding}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* Missing Documents */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-dealverse-navy">Missing Documents</CardTitle>
              <CardDescription className="text-dealverse-medium-gray">
                Critical documents requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {missingDocuments.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-dealverse-coral/5 border border-dealverse-coral/20">
                    <div className="flex items-center space-x-2">
                      <XCircle className="h-4 w-4 text-dealverse-coral" />
                      <span className="text-sm text-dealverse-navy">{doc}</span>
                    </div>
                    <Button variant="outline" size="sm" className="text-xs">
                      Request
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* AI Analysis Summary */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-dealverse-navy">AI Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-dealverse-blue" />
                <span className="font-medium text-dealverse-navy">Overall Risk Score</span>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-dealverse-amber">Medium</div>
                <Progress value={58} className="h-3 mt-2" />
                <p className="text-xs text-dealverse-medium-gray mt-1">
                  58/100 - Moderate risk factors identified
                </p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Documents Analyzed:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.documentsReviewed}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Anomalies Detected:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.riskFlags}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Confidence Level:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : '92%'}
                  </span>
                </div>
              </div>
              <Button className="w-full bg-dealverse-blue hover:bg-dealverse-blue/90">
                Generate Report
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Missing Documents */}
        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-dealverse-navy">Missing Documents</CardTitle>
              <CardDescription className="text-dealverse-medium-gray">
                Critical documents requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {missingDocuments.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-dealverse-coral/5 border border-dealverse-coral/20">
                    <div className="flex items-center space-x-2">
                      <XCircle className="h-4 w-4 text-dealverse-coral" />
                      <span className="text-sm text-dealverse-navy">{doc}</span>
                    </div>
                    <Button variant="outline" size="sm" className="text-xs">
                      Request
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* AI Analysis Summary */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-dealverse-navy">AI Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-dealverse-blue" />
                <span className="font-medium text-dealverse-navy">Overall Risk Score</span>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-dealverse-amber">Medium</div>
                <Progress value={58} className="h-3 mt-2" />
                <p className="text-xs text-dealverse-medium-gray mt-1">
                  58/100 - Moderate risk factors identified
                </p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Documents Analyzed:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.documentsReviewed}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Anomalies Detected:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.riskFlags}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-dealverse-medium-gray">Confidence Level:</span>
                  <span className="font-medium">
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : '92%'}
                  </span>
                </div>
              </div>
              <Button className="w-full bg-dealverse-blue hover:bg-dealverse-blue/90">
                Generate Report
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
