["tests/test_document_processing_pipeline.py::TestDocumentProcessingIntegration::test_multiple_file_processing", "tests/test_document_processing_pipeline.py::TestDocumentProcessingIntegration::test_processing_pipeline_performance", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_analyze_text_content", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_create_search_index", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_extract_enhanced_metadata", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_extract_text_content", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_generate_thumbnail", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_process_document_pipeline_success", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_process_document_pipeline_virus_detected", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_scan_for_viruses_direct", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_store_processing_artifacts", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_validate_file_enhanced", "tests/test_document_processing_pipeline.py::TestDocumentProcessor::test_virus_scanning"]