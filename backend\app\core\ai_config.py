"""
AI Configuration for DealVerse OS
"""
import os
from typing import Dict, Any, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class AISettings(BaseSettings):
    """AI service configuration settings"""

    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4-turbo-preview", env="OPENAI_MODEL")
    openai_max_tokens: int = Field(default=4000, env="OPENAI_MAX_TOKENS")
    openai_temperature: float = Field(default=0.1, env="OPENAI_TEMPERATURE")

    # Anthropic Configuration
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    anthropic_model: str = Field(default="claude-3-sonnet-20240229", env="ANTHROPIC_MODEL")
    anthropic_max_tokens: int = Field(default=4000, env="ANTHROPIC_MAX_TOKENS")
    anthropic_temperature: float = Field(default=0.1, env="ANTHROPIC_TEMPERATURE")

    # OpenRouter Configuration
    openrouter_api_key: Optional[str] = Field(default=None, env="OPENROUTER_API_KEY")
    openrouter_model: str = Field(default="deepseek/deepseek-chat", env="OPENROUTER_MODEL")
    openrouter_max_tokens: int = Field(default=4000, env="OPENROUTER_MAX_TOKENS")
    openrouter_temperature: float = Field(default=0.1, env="OPENROUTER_TEMPERATURE")
    openrouter_base_url: str = Field(default="https://openrouter.ai/api/v1", env="OPENROUTER_BASE_URL")
    openrouter_site_url: Optional[str] = Field(default="https://dealverse.com", env="OPENROUTER_SITE_URL")
    openrouter_site_name: Optional[str] = Field(default="DealVerse OS", env="OPENROUTER_SITE_NAME")

    # AI Service Configuration
    preferred_ai_provider: str = Field(default="openrouter", env="AI_PROVIDER")  # "openai", "anthropic", or "openrouter"
    enable_fallback: bool = Field(default=True, env="AI_ENABLE_FALLBACK")
    request_timeout: int = Field(default=60, env="AI_REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    
    # Document Processing Configuration
    max_document_size: int = Field(default=10 * 1024 * 1024, env="MAX_DOCUMENT_SIZE")  # 10MB
    chunk_size: int = Field(default=4000, env="DOCUMENT_CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="DOCUMENT_CHUNK_OVERLAP")
    
    # Analysis Configuration
    enable_entity_extraction: bool = Field(default=True, env="ENABLE_ENTITY_EXTRACTION")
    enable_risk_assessment: bool = Field(default=True, env="ENABLE_RISK_ASSESSMENT")
    enable_compliance_check: bool = Field(default=True, env="ENABLE_COMPLIANCE_CHECK")
    enable_financial_analysis: bool = Field(default=True, env="ENABLE_FINANCIAL_ANALYSIS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


# AI Prompts Configuration
AI_PROMPTS = {
    "document_analysis": {
        "system": """You are an expert investment banking analyst specializing in document analysis for M&A transactions, due diligence, and financial analysis. Your role is to provide comprehensive, accurate, and actionable insights from business documents.

Key responsibilities:
1. Extract and analyze key financial metrics, ratios, and trends
2. Identify potential risks, red flags, and areas of concern
3. Extract important entities (companies, people, dates, amounts)
4. Assess compliance with regulatory requirements
5. Provide clear, structured analysis with confidence scores

Always provide specific, quantifiable insights and flag any uncertainties or missing information.""",
        
        "user_template": """Analyze the following document content and provide a comprehensive analysis:

Document Type: {document_type}
Document Title: {document_title}
Analysis Type: {analysis_type}

Content:
{document_content}

Please provide:
1. Executive Summary (2-3 sentences)
2. Key Findings (top 5 most important insights)
3. Financial Analysis (if applicable)
4. Risk Assessment (identify potential risks and their severity)
5. Entity Extraction (companies, people, dates, amounts)
6. Compliance Notes (regulatory considerations)
7. Recommendations (actionable next steps)

Format your response as structured JSON with confidence scores for each finding."""
    },
    
    "entity_extraction": {
        "system": """You are an expert at extracting structured entities from business documents. Extract all relevant entities with high precision and provide confidence scores.""",
        
        "user_template": """Extract entities from this document content:

{document_content}

Extract the following entity types:
- Companies/Organizations
- People/Individuals  
- Financial Amounts
- Dates
- Locations
- Legal Terms/Clauses
- Key Metrics/KPIs

Return as structured JSON with entity type, value, context, and confidence score (0-1)."""
    },
    
    "risk_assessment": {
        "system": """You are a senior risk analyst specializing in investment banking and M&A transactions. Assess documents for potential risks, red flags, and areas requiring further investigation.""",
        
        "user_template": """Perform a comprehensive risk assessment of this document:

Document Type: {document_type}
Content: {document_content}

Analyze for:
1. Financial Risks (liquidity, solvency, profitability)
2. Operational Risks (business model, market position)
3. Legal/Regulatory Risks (compliance, litigation)
4. Strategic Risks (competitive threats, market changes)
5. Data Quality Issues (missing information, inconsistencies)

Provide risk level (Low/Medium/High/Critical) and detailed explanations."""
    },
    
    "compliance_check": {
        "system": """You are a compliance expert specializing in financial regulations, securities law, and investment banking compliance requirements.""",
        
        "user_template": """Review this document for compliance considerations:

Document Type: {document_type}
Content: {document_content}

Check for:
1. SEC disclosure requirements
2. Anti-money laundering (AML) considerations
3. Know Your Customer (KYC) requirements
4. GDPR/Privacy compliance
5. Industry-specific regulations
6. Missing required disclosures

Flag any compliance issues with severity levels and recommendations."""
    }
}

# Risk scoring configuration
RISK_SCORING_CONFIG = {
    "weights": {
        "financial_risk": 0.3,
        "operational_risk": 0.25,
        "legal_risk": 0.25,
        "strategic_risk": 0.2
    },
    "thresholds": {
        "low": 30,
        "medium": 60,
        "high": 80,
        "critical": 100
    }
}

# Entity extraction configuration
ENTITY_EXTRACTION_CONFIG = {
    "confidence_threshold": 0.7,
    "max_entities_per_type": 50,
    "entity_types": [
        "PERSON",
        "ORG", 
        "MONEY",
        "DATE",
        "GPE",  # Geopolitical entity
        "PERCENT",
        "CARDINAL",  # Numbers
        "LAW",
        "PRODUCT"
    ]
}


def get_ai_settings() -> AISettings:
    """Get AI configuration settings"""
    return AISettings()


def get_ai_prompt(prompt_type: str, analysis_type: str = "system") -> str:
    """Get AI prompt template"""
    return AI_PROMPTS.get(prompt_type, {}).get(analysis_type, "")


def validate_ai_configuration() -> Dict[str, Any]:
    """Validate AI configuration and return status"""
    settings = get_ai_settings()
    status = {
        "openai_configured": bool(settings.openai_api_key),
        "anthropic_configured": bool(settings.anthropic_api_key),
        "openrouter_configured": bool(settings.openrouter_api_key),
        "preferred_provider": settings.preferred_ai_provider,
        "fallback_enabled": settings.enable_fallback
    }

    # Check if at least one provider is configured
    if not (status["openai_configured"] or status["anthropic_configured"] or status["openrouter_configured"]):
        status["error"] = "No AI provider configured. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or OPENROUTER_API_KEY"

    return status
