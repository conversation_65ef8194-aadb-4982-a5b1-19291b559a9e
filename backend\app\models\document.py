"""
Document model for file management and due diligence
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, JSON, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Document(BaseModel):
    """Document model for file storage and management"""
    
    __tablename__ = "documents"
    
    # Basic document information
    title = Column(String(255), nullable=False)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)  # S3 path or local path
    file_size = Column(Integer)  # Size in bytes
    file_type = Column(String(100))  # MIME type
    file_extension = Column(String(10))
    
    # Document categorization
    document_type = Column(String(100))  # financial, legal, operational, marketing, etc.
    category = Column(String(100))
    subcategory = Column(String(100))
    
    # Document status
    status = Column(String(50), default="uploaded")  # uploaded, processing, analyzed, approved, rejected
    is_confidential = Column(Boolean, default=False)
    is_archived = Column(Boolean, default=False)
    
    # Version control
    version = Column(String(20), default="1.0")
    is_latest_version = Column(Boolean, default=True)
    parent_document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"))
    
    # AI Analysis results
    ai_analysis = Column(JSON, default=dict)  # AI analysis results
    risk_score = Column(String(3))  # 0-100 risk score
    key_findings = Column(JSON, default=list)
    extracted_data = Column(JSON, default=dict)
    
    # Compliance and review
    compliance_status = Column(String(50), default="pending")  # pending, compliant, non_compliant, review_required
    review_status = Column(String(50), default="pending")  # pending, approved, rejected, needs_revision
    reviewer_notes = Column(Text)
    
    # Metadata
    description = Column(Text)
    tags = Column(JSON, default=list)
    keywords = Column(JSON, default=list)
    
    # Access control
    access_level = Column(String(50), default="organization")  # public, organization, deal_team, restricted
    allowed_roles = Column(JSON, default=list)
    
    # Organization relationship
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    organization = relationship("Organization", back_populates="documents")
    
    # Deal relationship (optional)
    deal_id = Column(UUID(as_uuid=True), ForeignKey("deals.id"))
    deal = relationship("Deal", back_populates="documents")
    
    # Client relationship (optional)
    client_id = Column(UUID(as_uuid=True), ForeignKey("clients.id"))
    client = relationship("Client", back_populates="documents")
    
    # User relationship (uploader)
    uploaded_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    uploaded_by = relationship("User", back_populates="created_documents")
    
    # Version relationships
    parent_document = relationship("Document", remote_side="Document.id")
    child_documents = relationship("Document", back_populates="parent_document")

    # Analysis relationships
    analyses = relationship("DocumentAnalysis", back_populates="document")
    reviews = relationship("DocumentReview", back_populates="document")

    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', type='{self.document_type}', status='{self.status}')>"
